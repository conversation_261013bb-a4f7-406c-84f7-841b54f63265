use serde_json::Value;
use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::Mutex;
use crate::{SubscriptionManager, transaction_utils};

/// Enhanced transaction processor that only processes transactions for subscribed mints
pub struct EnhancedTransactionProcessor {
    pub subscription_manager: Arc<Mutex<SubscriptionManager>>,
    pub successful_tx_count: u64,
    pub total_tx_count: u64,
    pub alerted_tokens: HashSet<String>,
    pub last_activity: HashMap<String, Instant>,
}

impl EnhancedTransactionProcessor {
    pub fn new(subscription_manager: Arc<Mutex<SubscriptionManager>>) -> Self {
        Self {
            subscription_manager,
            successful_tx_count: 0,
            total_tx_count: 0,
            alerted_tokens: HashSet::new(),
            last_activity: HashMap::new(),
        }
    }

    /// Process a logsNotification message with simplified processing
    /// Since <PERSON><PERSON> only sends transactions for subscribed mints, we don't need complex filtering
    pub async fn process_logs_notification(
        &mut self,
        json: &Value,
        window_map: &mut HashMap<String, crate::WindowStats>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        self.total_tx_count += 1;

        // Debug: Log every 100th transaction to show we're receiving data
        if self.total_tx_count % 100 == 0 {
            println!("🔍 Debug: Processed {} total transactions", self.total_tx_count);
        }

        // Check if transaction was successful
        if !transaction_utils::is_transaction_successful(json) {
            // Debug: Log failed transactions occasionally
            if self.total_tx_count % 500 == 0 {
                println!("⚠️  Debug: Transaction #{} failed success check", self.total_tx_count);
            }
            return Ok(()); // Early exit for failed transactions
        }

        // Only process successful transactions from here
        self.successful_tx_count += 1;

        // Extract transaction signature
        let signature = transaction_utils::extract_signature(json);

        // Try to determine the specific mint for this transaction using subscription ID
        let subscription_id = json.pointer("/params/subscription").and_then(|s| s.as_u64());

        let target_mint = if let Some(sub_id) = subscription_id {
            let manager = self.subscription_manager.lock().await;
            manager.get_mint_for_subscription(sub_id)
        } else {
            None
        };

        // Debug: Log subscription status every 50th successful transaction
        if self.successful_tx_count % 50 == 0 {
            let manager = self.subscription_manager.lock().await;
            let active_mints = manager.get_active_mints();
            println!("🔍 Debug: {} successful transactions processed, {} active subscriptions: {:?}",
                     self.successful_tx_count, active_mints.len(), active_mints);

            // Debug: Show subscription ID for this transaction
            if let Some(sub_id) = subscription_id {
                println!("🔍 Debug: Transaction subscription_id: {}", sub_id);
            } else {
                println!("⚠️  Debug: No subscription_id found in transaction");
            }
        }

        // Process the transaction
        if let Some(mint) = target_mint {
            println!("🎯 Processing transaction for mint: {} (signature: {})",
                     mint, signature.as_deref().unwrap_or("<none>"));
            self.process_mint_transaction(json, &mint, &signature, window_map).await?;
        } else {
            // Fallback: use first active mint if we can't determine the specific one
            let fallback_mint = {
                let manager = self.subscription_manager.lock().await;
                let active_mints = manager.get_active_mints();
                active_mints.first().cloned()
            };

            if let Some(mint) = fallback_mint {
                println!("🔄 Processing transaction with fallback mint: {} (signature: {})",
                         mint, signature.as_deref().unwrap_or("<none>"));
                self.process_mint_transaction(json, &mint, &signature, window_map).await?;
            } else {
                // No active subscriptions - this shouldn't happen if we're receiving transactions
                if self.successful_tx_count % 100 == 0 {
                    println!("⚠️  Debug: Received transaction but no active subscriptions");
                }
            }
        }

        Ok(())
    }

    /// Process a transaction for a specific mint with clean, focused output
    async fn process_mint_transaction(
        &mut self,
        json: &Value,
        mint: &str,
        signature: &Option<String>,
        window_map: &mut HashMap<String, crate::WindowStats>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Extract logs for analysis
        let empty_logs = vec![];
        // For logsNotification, logs are under /params/result/value/logs
        let logs = json.pointer("/params/result/value/logs")
            .and_then(|l| l.as_array())
            .or_else(|| {
                // Fallback for transactionNotification format (backward compatibility)
                json.pointer("/params/result/value/meta/logMessages").and_then(|l| l.as_array())
            })
            .unwrap_or(&empty_logs);

        // Enhanced buy/sell detection
        let transaction_type = transaction_utils::detect_buy_sell_type(logs);

        // Extract SOL amount with safer defaults for logsNotification
        let sol_amount = transaction_utils::extract_sol_amount(json)
            .unwrap_or(0.01); // Default to 0.01 SOL if can't extract

        // Convert SOL to lamports for consistent tracking with overflow protection
        let amount_lamports = if sol_amount > 1000.0 {
            // If amount seems too large, cap it to prevent overflow
            1_000_000_000u64 // 1 SOL in lamports
        } else {
            (sol_amount * 1_000_000_000.0) as u64
        };

        match transaction_type {
            Some(true) => {
                // Record buy in sliding window
                let stats = window_map.entry(mint.to_string()).or_insert_with(crate::WindowStats::new);
                stats.record_buy(amount_lamports);

                // Pure threshold-crossing detection: alert on >4, reset after inactivity
                let (buy_count, _, _, _) = stats.totals();
                let now = Instant::now();

                // Track activity for this token
                self.last_activity.insert(mint.to_string(), now);

                if buy_count > 4 && !self.alerted_tokens.contains(mint) {
                    println!("🚨 Token {} has {} buys in the last 3 s! Signature: {}",
                             mint, buy_count, signature.as_deref().unwrap_or("<none>"));
                    self.alerted_tokens.insert(mint.to_string());
                }

                // Clean up alerted tokens that have been inactive for 10+ seconds
                self.cleanup_inactive_alerts(now);
            }
            Some(false) => {
                // Record sell in sliding window
                let stats = window_map.entry(mint.to_string()).or_insert_with(crate::WindowStats::new);
                stats.record_sell(amount_lamports);
            }
            None => {
                // No clear buy/sell pattern detected - this is normal for many transactions
            }
        }

       

        Ok(())
    }

    /// Clean up alerted tokens that have been inactive for a while
    fn cleanup_inactive_alerts(&mut self, now: Instant) {
        let inactive_tokens: Vec<String> = self.alerted_tokens
            .iter()
            .filter(|token| {
                if let Some(last_activity) = self.last_activity.get(*token) {
                    // Remove from alerted set if no activity for 10+ seconds
                    now.duration_since(*last_activity).as_secs() >= 10
                } else {
                    // Remove if we don't have activity tracking for this token
                    true
                }
            })
            .cloned()
            .collect();

        for token in inactive_tokens {
            self.alerted_tokens.remove(&token);
        }

        // Also clean up old activity entries to prevent memory growth
        self.last_activity.retain(|_, &mut last_time| {
            now.duration_since(last_time).as_secs() < 60
        });
    }


}

/// Helper function to validate that we're receiving transactions for the correct mints
pub async fn validate_subscription_effectiveness(
    subscription_manager: &Arc<Mutex<SubscriptionManager>>,
    processed_mints: &HashMap<String, crate::WindowStats>,
) {
    let subscribed_mints = {
        let manager = subscription_manager.lock().await;
        manager.get_active_mints()
    };

    println!("🔍 Subscription Validation:");
    println!("   • Subscribed mints: {}", subscribed_mints.len());
    println!("   • Mints with activity: {}", processed_mints.len());

    for mint in &subscribed_mints {
        if processed_mints.contains_key(mint) {
            println!("   ✅ {} - receiving transactions", mint);
        } else {
            println!("   ⚠️  {} - no transactions received yet", mint);
        }
    }

    // Check for unexpected mints (shouldn't happen with proper filtering)
    for mint in processed_mints.keys() {
        if !subscribed_mints.contains(mint) {
            println!("   ❌ {} - unexpected mint activity (not subscribed)", mint);
        }
    }
}

/// Test function to debug why we're not detecting pump.fun transactions
pub async fn debug_pump_fun_detection() {
    println!("🧪 Testing pump.fun transaction detection patterns...");

    // Test our buy/sell detection with typical pump.fun patterns
    let test_logs = vec![
        serde_json::json!("Program log: Instruction: Buy"),
        serde_json::json!("Program log: Instruction: Sell"),
        serde_json::json!("Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1]"),
        serde_json::json!("Program log: buy"),
        serde_json::json!("Program log: sell"),
        serde_json::json!("Program log: swap"),
    ];

    let buy_result = crate::transaction_utils::detect_buy_sell_type(&test_logs);
    println!("   Buy/sell detection result: {:?}", buy_result);

    // Test mint address validation
    let test_mint = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"; // pump.fun program
    println!("   Test mint validation: {}", crate::transaction_utils::is_valid_mint_address(test_mint));
}
